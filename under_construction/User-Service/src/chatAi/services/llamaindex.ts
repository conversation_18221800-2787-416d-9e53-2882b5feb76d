import * as FormData from 'form-data';

interface LlamaIndexResponse {
  id: string;
  status?: string;
  error?: string;
}

interface RetrieveResponse {
  nodes: Array<{
    text: string;
    metadata: {
      page?: number;
      source?: string;
      file_name?: string;
    };
    score: number;
  }>;
}

interface FileUploadResponse {
  id: string;
  name: string;
  status: string;
}

interface PipelineResponse {
  id: string;
  name: string;
  status: string;
}

interface ProjectResponse {
  id: string;
  name: string;
  description?: string;
}

interface RetrieverResponse {
  id: string;
  name: string;
  project_id: string;
}

export class LlamaIndexService {
  private apiKey: string;
  private baseUrl = 'https://api.cloud.llamaindex.ai/api/v1';
  private isConfigured: boolean;
  private projectId: string;

  constructor() {
    this.apiKey =
      process.env.LLAMA_CLOUD_API_KEY || process.env.LLAMACLOUD_API_KEY || '';
    this.projectId = process.env.LLAMA_CLOUD_PROJECT_ID || '';
    this.isConfigured = !!this.apiKey;
    console.log(this.apiKey, 'llama-key');
    if (!this.apiKey) {
      console.warn(
        'WARNING: LLAMA_CLOUD_API_KEY not configured. Document indexing will be disabled.',
      );
      console.warn(
        'For full functionality, please set LLAMA_CLOUD_API_KEY in Railway environment variables.',
      );
    } else {
      console.log('LlamaIndexService initialized successfully');
      if (!this.projectId) {
        console.warn(
          'WARNING: LLAMA_CLOUD_PROJECT_ID not configured. Using default project.',
        );
      }
    }
  }

  private checkConfiguration(): void {
    if (!this.isConfigured) {
      throw new Error(
        'Document indexing service is currently unavailable. Please try again later.',
      );
    }
  }

  /**
   * Sanitizes third-party API errors to prevent exposing sensitive information
   */
  private sanitizeError(error: any, context: string): Error {
    // Log the actual error for debugging
    console.error(`LlamaIndex ${context} error:`, error);

    // Return a generic user-friendly message
    if (error?.message?.includes('timeout') || error?.name === 'AbortError') {
      return new Error(
        'Document indexing is taking longer than expected. Please try again.',
      );
    }

    if (error?.message?.includes('401') || error?.message?.includes('403')) {
      return new Error(
        'Document indexing service is temporarily unavailable. Please try again later.',
      );
    }

    if (error?.message?.includes('429')) {
      return new Error(
        'Document indexing service is busy. Please try again in a few moments.',
      );
    }

    // Generic fallback message
    return new Error('Unable to index document. Please try again.');
  }

  // ==================== Working LlamaCloud API Methods (tosky_test project) ====================

  /**
   * List existing projects
   */
  async listProjects(): Promise<ProjectResponse[]> {
    this.checkConfiguration();

    try {
      const response = await fetch(`${this.baseUrl}/projects`, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex listProjects error: ${response.status} - ${errorText}`,
        );
        return [];
      }

      const result = await response.json();
      console.log(`Found ${result.length || 0} existing projects`);
      return result;
    } catch (error) {
      console.error(`LlamaIndex listProjects error:`, error);
      return [];
    }
  }

  /**
   * Create a new project
   */
  async createProject(projectName: string): Promise<ProjectResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        name: projectName,
        description: `Project for ${projectName}`,
      };

      const response = await fetch(`${this.baseUrl}/projects`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex createProject error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Project creation failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`Project created: ${result.id}`);
      return result;
    } catch (error) {
      console.error(`LlamaIndex createProject error:`, error);
      throw this.sanitizeError(error, 'createProject');
    }
  }

  /**
   * Get or create the tosky_test project (matches referenceCode)
   */
  async getOrCreateDefaultProject(): Promise<ProjectResponse> {
    try {
      // First, list all projects to see what's actually available with this API key
      console.log('🔍 Checking projects associated with API key...');
      const projects = await this.listProjects();

      if (projects && projects.length > 0) {
        const project = projects[0];
        console.log(`✅ Using project: ${project.id} (${project.name})`);
        return project;
      } else {
        console.log('📋 No projects found, creating tosky_test project...');
        return await this.createProject('tosky_test');
      }
    } catch (error) {
      console.error('❌ Error accessing projects:', error);
      throw this.sanitizeError(error, 'getOrCreateDefaultProject');
    }
  }

  /**
   * Upload a file to LlamaCloud with project ID
   */
  async uploadFile(
    fileBuffer: Buffer,
    fileName: string,
    projectId: string,
  ): Promise<FileUploadResponse> {
    this.checkConfiguration();

    try {
      const formData = new FormData();
      const uint8Array = new Uint8Array(fileBuffer);
      const blob = new Blob([uint8Array], {
        type: this.getContentType(fileName),
      });
      formData.append('upload_file', blob, fileName);
      formData.append('project_id', projectId);

      const response = await fetch(`${this.baseUrl}/files`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: formData as any,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaCloud file upload error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'uploadFile',
        );
      }

      const result = await response.json();
      console.log(`File uploaded: ${result.id}`);
      return result;
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'uploadFile');
    }
  }

  /**
   * Get content type for file
   */
  private getContentType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();
    switch (ext) {
      case 'pdf':
        return 'application/pdf';
      case 'txt':
        return 'text/plain';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * Create a pipeline with project and file IDs
   */
  async createPipeline(
    pipelineName: string,
    projectId: string,
    fileIds: string[],
  ): Promise<PipelineResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        name: pipelineName,
        project_id: projectId,
        file_ids: fileIds,
      };

      const response = await fetch(`${this.baseUrl}/pipelines`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex createPipeline error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Pipeline creation failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`Pipeline created: ${result.id}`);
      return result;
    } catch (error) {
      console.error(`LlamaIndex createPipeline error:`, error);
      throw this.sanitizeError(error, 'createPipeline');
    }
  }

  /**
   * Create a retriever for querying documents
   */
  async createRetriever(
    retrieverName: string,
    projectId: string,
    pipelineIds: string[],
  ): Promise<RetrieverResponse> {
    this.checkConfiguration();

    try {
      const payload = {
        name: retrieverName,
        project_id: projectId,
        pipeline_ids: pipelineIds,
      };

      const response = await fetch(`${this.baseUrl}/retrievers`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex createRetriever error: ${response.status} - ${errorText}`,
        );
        throw new Error(`Retriever creation failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`Retriever created: ${result.id}`);
      return result;
    } catch (error) {
      console.error(`LlamaIndex createRetriever error:`, error);
      throw this.sanitizeError(error, 'createRetriever');
    }
  }

  /**
   * List existing pipelines
   */
  async listPipelines(): Promise<PipelineResponse[]> {
    this.checkConfiguration();

    try {
      const response = await fetch(`${this.baseUrl}/pipelines`, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex listPipelines error: ${response.status} - ${errorText}`,
        );
        return [];
      }

      const result = await response.json();
      console.log(`Found ${result.length || 0} existing pipelines`);
      return result;
    } catch (error) {
      console.error(`LlamaIndex listPipelines error:`, error);
      return [];
    }
  }

  /**
   * Get or create the default tosky_test pipeline
   */
  async getOrCreateDefaultPipeline(
    projectId: string,
  ): Promise<PipelineResponse> {
    this.checkConfiguration();

    try {
      // List existing pipelines to see if tosky_test exists
      const pipelines = await this.listPipelines();

      // Look for tosky_test pipeline
      const existingPipeline = pipelines.find((p) => p.name === 'tosky_test');
      if (existingPipeline) {
        console.log(
          `♻️ Using existing tosky_test pipeline: ${existingPipeline.id}`,
        );
        return existingPipeline;
      }

      // If no pipelines exist, assume there's a default tosky_test pipeline
      // that's not visible through the API but exists in the project
      console.log(`♻️ Using default tosky_test pipeline (assuming it exists)`);
      return {
        id: 'tosky_test', // Use the name as ID for now
        name: 'tosky_test',
        status: 'ready',
      };
    } catch (error) {
      console.error(`❌ Error getting default pipeline:`, error);
      // Return a default pipeline object
      return {
        id: 'tosky_test',
        name: 'tosky_test',
        status: 'ready',
      };
    }
  }

  /**
   * Create vector index from parsed document using working project-based workflow
   */
  async createIndex(
    parsedData: any,
    documentName: string,
    fileBuffer?: Buffer,
  ): Promise<LlamaIndexResponse> {
    this.checkConfiguration();

    try {
      console.log(`🔍 Creating vector index for: ${documentName}`);

      // Step 1: Get or create tosky_test project (matches referenceCode)
      const project = await this.getOrCreateDefaultProject();

      // Step 2: Create file buffer from parsed text
      const textBuffer = Buffer.from(parsedData.text, 'utf-8');

      // Step 3: Upload file
      const file = await this.uploadFile(textBuffer, documentName, project.id);

      // Step 4: Get or use the default tosky_test pipeline
      const pipeline = await this.getOrCreateDefaultPipeline(project.id);

      // Step 5: Create retriever with highly unique name
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const uuid = Math.random().toString(36).substring(2, 15);
      const retrieverName = `retr_${timestamp}_${randomId}_${uuid}`;
      const retriever = await this.createRetriever(retrieverName, project.id, [
        pipeline.id,
      ]);

      console.log(`✅ Vector index created with retriever: ${retriever.id}`);

      return {
        id: retriever.id,
        status: 'SUCCESS',
      };
    } catch (error) {
      console.error(
        `❌ LlamaIndex createIndex error for ${documentName}:`,
        error,
      );
      throw this.sanitizeError(error, 'createIndex');
    }
  }

  /**
   * Retrieve relevant chunks from vector index using retriever
   */
  async retrieve(
    retrieverId: string,
    query: string,
    topK: number = 5,
  ): Promise<RetrieveResponse> {
    this.checkConfiguration();

    try {
      console.log(
        `🔍 Retrieving from retriever ${retrieverId} for query: "${query.substring(0, 50)}..."`,
      );

      const payload = {
        query,
        top_k: topK,
      };

      const response = await fetch(
        `${this.baseUrl}/retrievers/${retrieverId}/retrieve`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex retrieve error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'retrieve',
        );
      }

      const result = await response.json();
      console.log(`✅ Retrieved ${result.nodes?.length || 0} relevant chunks`);

      // Transform the response to match the expected format
      return {
        nodes:
          result.retrieval_nodes?.map((node: any) => ({
            text: node.text || '',
            metadata: {
              page: node.metadata?.page || 0,
              source: node.metadata?.source || '',
              file_name: node.metadata?.file_name || '',
            },
            score: node.score || 0,
          })) || [],
      };
    } catch (error) {
      console.error(`LlamaIndex retrieve error:`, error);
      throw this.sanitizeError(error, 'retrieve');
    }
  }

  async deleteIndex(retrieverId: string): Promise<LlamaIndexResponse> {
    this.checkConfiguration();

    try {
      console.log(`🗑️ Deleting retriever: ${retrieverId}`);

      const response = await fetch(
        `${this.baseUrl}/retrievers/${retrieverId}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${this.apiKey}`,
          },
        },
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error(
          `LlamaIndex delete retriever error: ${response.status} - ${errorText}`,
        );
        throw this.sanitizeError(
          { status: response.status, message: errorText },
          'deleteIndex',
        );
      }

      console.log(`✅ Retriever deleted: ${retrieverId}`);
      return {
        id: retrieverId,
        status: 'DELETED',
      };
    } catch (error) {
      if (
        error instanceof Error &&
        error.message.includes('Document indexing')
      ) {
        throw error;
      }
      throw this.sanitizeError(error, 'deleteIndex');
    }
  }
}

export const llamaIndexService = new LlamaIndexService();
